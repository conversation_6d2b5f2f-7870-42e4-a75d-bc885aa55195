# 🔬 Decodificador de Protocolo Agar.io

Este script intercepta e decodifica as mensagens binárias do protocolo Agar.io em tempo real.

## 📋 Funcionalidades

### 🎯 Tipos de Pacotes Suportados

| Código | Tipo              | Descrição                       |
| ------ | ----------------- | ------------------------------- |
| 16     | UPDATE_NODES      | Atualização das células no mapa |
| 17     | UPDATE_POSITION   | Posição da câmera/jogador       |
| 18     | CLEAR_ALL         | Limpar todas as células         |
| 32     | OWN_ID            | IDs das próprias células        |
| 49     | FFA_LEADERBOARD   | Leaderboard FFA                 |
| 50     | TEAMS_LEADERBOARD | Leaderboard por equipes         |
| 64     | BORDER            | Bordas do mapa                  |
| 81     | DRAW_LINE         | Linhas visuais                  |

### ⌨️ Controles (Keybinds)

| Tecla  | Função                                               |
| ------ | ---------------------------------------------------- |
| **F1** | Mostra ajuda                                         |
| **F2** | Status do WebSocket + mensagens recentes (30s)       |
| **F3** | Análise detalhada dos pacotes (estatísticas + dados) |
| **F4** | Filtrar mensagens por tipo específico                |

## 🚀 Como Usar

1. **Instalar o script** no Tampermonkey
2. **Abrir o Agar.io** (agariobr.com.br)
3. **Abrir o Console** (F12 → Console)
4. **Conectar ao jogo** - o script detecta automaticamente
5. **Usar os keybinds** para analisar os dados

## 📊 Exemplo de Dados Decodificados

### UPDATE_NODES (Células)

```javascript
{
  cells: [
    {
      id: 12345,
      x: 1000,
      y: 2000,
      size: 150,
      flags: {
        isVirus: false,
        isAgitated: false,
        isEjectedMass: false,
        isFood: true
      },
      color: { r: 255, g: 100, b: 50 },
      name: "PlayerName",
      skin: ""
    }
  ],
  destroyedCells: [11111, 22222]
}
```

### FFA_LEADERBOARD

```javascript
{
	leaderboard: [
		{ position: 1, name: "TopPlayer", isMe: false },
		{ position: 2, name: "MyName", isMe: true },
		{ position: 3, name: "ThirdPlace", isMe: false },
	];
}
```

### BORDER (Limites do Mapa)

```javascript
{
  minX: -7071.067,
  minY: -7071.067,
  maxX: 7071.067,
  maxY: 7071.067
}
```

## 🔍 Análise Avançada

### Estatísticas de Tráfego

O script conta automaticamente:

-   Frequência de cada tipo de pacote
-   Direção (enviado/recebido)
-   Volume de dados por segundo

### Hex Dump

Cada mensagem inclui um hex dump dos primeiros 128 bytes para análise detalhada:

```
00000000: 10 02 00 39 30 00 00 e8 03 00 00 d0 07 00 00 96 |...90...........|
00000010: 00 02 ff 64 32 00 00 50 6c 61 79 65 72 00 00 00 |...d2..Player...|
```

## 🛠️ Personalização

### Adicionar Novos Tipos de Pacote

```typescript
private static readonly PACKET_TYPES: Record<number, string> = {
  // Adicione novos tipos aqui
  99: "CUSTOM_PACKET",
};
```

### Modificar Buffer de Mensagens

```typescript
// Altere o tamanho do buffer (padrão: 100 mensagens)
if (this.messageBuffer.length > 200) {
	this.messageBuffer = this.messageBuffer.slice(-200);
}
```

## 🔧 Troubleshooting

### Script não detecta mensagens?

-   Verifique se está no site correto (agariobr.com.br)
-   Certifique-se de que o WebSocket está conectado (F2)
-   Recarregue a página

### Console muito poluído?

-   Use F4 para filtrar tipos específicos
-   Diminua o intervalo de logging (padrão: 3s)

### Dados não decodificados?

-   Alguns pacotes podem ser desconhecidos
-   O protocolo pode ter mudado
-   Verifique o hex dump para análise manual

## 📝 Notas Técnicas

-   **Endianness**: Little-endian para todos os números
-   **Strings**: UTF-16 terminadas em null
-   **Buffer**: Mantém últimas 100 mensagens
-   **Performance**: Logging a cada 3 segundos
-   **Compatibilidade**: Testado no Agar.io Brasil

## 🎮 Dicas de Uso

1. **Use F3** para ver padrões de comportamento
2. **Use F4** para focar em tipos específicos (ex: "UPDATE_NODES")
3. **Monitor o leaderboard** com "FFA_LEADERBOARD"
4. **Analise movimentos** com "UPDATE_POSITION"
5. **Estude células** com "UPDATE_NODES"

---

_Desenvolvido para fins educacionais e análise de protocolo._
