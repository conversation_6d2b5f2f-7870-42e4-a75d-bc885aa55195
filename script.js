"use strict";
// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==
// ===== CONFIGURAÇÕES =====
const CONFIG = {
	SCRIPT_NAME: "teste",
	VERSION: "1.0.30",
	STORAGE_KEYS: {
		SETTINGS: "settings",
	},
};
// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
	static get(key) {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : null;
		} catch (_a) {
			return null;
		}
	}
	static setJSON(key, value) {
		try {
			localStorage.setItem(key, JSON.stringify(value));
			return true;
		} catch (_a) {
			return false;
		}
	}
	static remove(key) {
		try {
			localStorage.removeItem(key);
			return true;
		} catch (_a) {
			return false;
		}
	}
}
class DOMUtilities {
	static createElement(tag, options = {}) {
		const element = document.createElement(tag);
		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);
		if (options.attributes)
			Object.entries(options.attributes).forEach(([key, value]) => {
				element.setAttribute(key, value);
			});
		if (options.eventListeners)
			Object.entries(options.eventListeners).forEach(([event, listener]) => {
				element.addEventListener(event, listener);
			});
		return element;
	}
	static removeElement(element) {
		if (element && element.parentNode) {
			element.parentNode.removeChild(element);
			return true;
		}
		return false;
	}
}
class SettingsStore {
	constructor() {
		this.settings = {};
		this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
		this.loadSettings();
	}
	static getInstance() {
		if (!SettingsStore.instance) {
			SettingsStore.instance = new SettingsStore();
		}
		return SettingsStore.instance;
	}
	loadSettings() {
		const savedSettings = StorageService.get(this.storageKey);
		if (savedSettings && typeof savedSettings === "object") {
			this.settings = savedSettings;
		}
	}
	saveSettings() {
		return StorageService.setJSON(this.storageKey, this.settings);
	}
	getAllSettings() {
		return Object.assign({}, this.settings);
	}
	getSetting(key) {
		return this.settings[key] || null;
	}
	setSetting(key, value) {
		this.settings[key] = value;
		return this.saveSettings();
	}
}
class KeyBindManager {
	constructor() {
		this.keyBindings = new Map();
		this.setupGlobalListener();
	}
	static getInstance() {
		if (!KeyBindManager.instance) {
			KeyBindManager.instance = new KeyBindManager();
		}
		return KeyBindManager.instance;
	}
	setupGlobalListener() {
		document.addEventListener("keydown", event => {
			const key = event.key.toLowerCase();
			const binding = this.keyBindings.get(key);
			if (binding) {
				const result = binding.handler(event);
				if (result !== false) {
					event.preventDefault();
					event.stopPropagation();
				}
			}
		});
	}
	register(binding) {
		const key = binding.key.toLowerCase();
		this.keyBindings.set(key, binding);
		return true;
	}
	listBindings() {
		return Array.from(this.keyBindings.values());
	}
}
class DataAnalyzer {
	static logRawBuffer(data) {
		try {
			const now = new Date();
			const timestamp = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}:${now
				.getSeconds()
				.toString()
				.padStart(2, "0")}.${now.getMilliseconds().toString().padStart(3, "0")}`;
			if (data instanceof ArrayBuffer) {
				const u8 = new Uint8Array(data);
				const opcode = u8.length > 0 ? u8[0] : null;
				const opcodeInfo = opcode !== null ? this.KNOWN_OPCODES[opcode] : null;
				console.group(`🔍 [${timestamp}] Raw Buffer Analysis`);
				console.log(`📊 Size: ${u8.length} bytes`);
				console.log(`🎯 Opcode: ${opcode} ${opcodeInfo ? `(${opcodeInfo.name})` : "(UNKNOWN)"}`);
				if (opcodeInfo) {
					console.log(`📝 Description: ${opcodeInfo.description}`);
				}
				console.log(`🔢 Raw Data:`, Array.from(u8));
				console.log(
					`🔤 Hex:`,
					Array.from(u8)
						.map(b => b.toString(16).padStart(2, "0"))
						.join(" ")
				);
				// Tentar parsear dados específicos baseado no opcode
				if (opcodeInfo === null || opcodeInfo === void 0 ? void 0 : opcodeInfo.parser) {
					try {
						const parsed = opcodeInfo.parser(u8);
						console.log(`✨ Parsed Data:`, parsed);
					} catch (e) {
						console.log(`❌ Parse Error:`, e);
					}
				}
				console.groupEnd();
			} else {
				console.log(`🔍 [${timestamp}] Non-ArrayBuffer data:`, data);
			}
		} catch (error) {
			console.error(`❌ Error in logRawBuffer:`, error);
		}
	}
	static parseMessage(data, type) {
		const message = {
			timestamp: Date.now(),
			type,
			rawData: data,
			size: 0,
		};
		if (data instanceof ArrayBuffer) {
			const u8 = new Uint8Array(data);
			message.size = u8.length;
			message.opcode = u8.length > 0 ? u8[0] : undefined;
			if (message.opcode !== undefined) {
				const opcodeInfo = this.KNOWN_OPCODES[message.opcode];
				if (opcodeInfo === null || opcodeInfo === void 0 ? void 0 : opcodeInfo.parser) {
					try {
						message.parsedData = opcodeInfo.parser(u8);
					} catch (e) {
						console.warn(`Failed to parse opcode ${message.opcode}:`, e);
					}
				}
			}
		} else if (typeof data === "string") {
			message.size = data.length;
		}
		return message;
	}
	static getOpcodeStats(messages) {
		const stats = {};
		messages.forEach(msg => {
			if (msg.opcode !== undefined) {
				if (!stats[msg.opcode]) {
					const opcodeInfo = this.KNOWN_OPCODES[msg.opcode];
					stats[msg.opcode] = {
						count: 0,
						name: (opcodeInfo === null || opcodeInfo === void 0 ? void 0 : opcodeInfo.name) || "UNKNOWN",
						lastSeen: 0,
					};
				}
				stats[msg.opcode].count++;
				stats[msg.opcode].lastSeen = Math.max(stats[msg.opcode].lastSeen, msg.timestamp);
			}
		});
		return stats;
	}
	// Análise avançada de padrões de opcodes
	static analyzeOpcodePatterns(messages) {
		const stats = this.getOpcodeStats(messages);
		const totalMessages = messages.length;
		// Classificar opcodes por frequência
		const sortedByFrequency = Object.entries(stats)
			.sort(([, a], [, b]) => b.count - a.count)
			.slice(0, 10);
		// Identificar opcodes mais recentes
		const sortedByRecency = Object.entries(stats)
			.sort(([, a], [, b]) => b.lastSeen - a.lastSeen)
			.slice(0, 5);
		// Calcular distribuição por tipo
		const knownOpcodes = Object.entries(stats).filter(([opcode]) => this.KNOWN_OPCODES[Number(opcode)]);
		const unknownOpcodes = Object.entries(stats).filter(([opcode]) => !this.KNOWN_OPCODES[Number(opcode)]);
		return {
			summary: {
				totalMessages,
				uniqueOpcodes: Object.keys(stats).length,
				knownOpcodes: knownOpcodes.length,
				unknownOpcodes: unknownOpcodes.length,
				knownPercentage: ((knownOpcodes.length / Object.keys(stats).length) * 100).toFixed(1) + "%",
			},
			topByFrequency: sortedByFrequency.map(([opcode, data]) => ({
				opcode: Number(opcode),
				name: data.name,
				count: data.count,
				percentage: ((data.count / totalMessages) * 100).toFixed(1) + "%",
			})),
			mostRecent: sortedByRecency.map(([opcode, data]) => ({
				opcode: Number(opcode),
				name: data.name,
				lastSeen: new Date(data.lastSeen).toLocaleTimeString(),
				count: data.count,
			})),
			unknownOpcodesList: unknownOpcodes
				.sort(([, a], [, b]) => b.count - a.count)
				.slice(0, 15)
				.map(([opcode, data]) => ({
					opcode: Number(opcode),
					count: data.count,
					lastSeen: new Date(data.lastSeen).toLocaleTimeString(),
				})),
		};
	}
	// Exportar dados para análise externa
	static exportOpcodeData(messages) {
		const analysis = this.analyzeOpcodePatterns(messages);
		const timestamp = new Date().toISOString();
		return JSON.stringify(
			{
				exportTimestamp: timestamp,
				totalMessages: messages.length,
				timeRange: {
					start: messages.length > 0 ? new Date(messages[0].timestamp).toISOString() : null,
					end: messages.length > 0 ? new Date(messages[messages.length - 1].timestamp).toISOString() : null,
				},
				analysis,
				rawStats: this.getOpcodeStats(messages),
			},
			null,
			2
		);
	}
}
DataAnalyzer.KNOWN_OPCODES = {
	// Opcodes de movimento e posição
	16: {
		name: "UPDATE_NODES",
		description: "Atualização de células/nós",
		parser: data => {
			if (data.length < 2) return null;
			const view = new DataView(data.buffer, data.byteOffset);
			return {
				opcode: data[0],
				nodeCount: data.length > 1 ? view.getUint16(1, true) : 0,
				rawSize: data.length,
			};
		},
	},
	17: {
		name: "UPDATE_POSITION",
		description: "Atualização de posição",
		parser: data => {
			if (data.length < 9) return null;
			const view = new DataView(data.buffer, data.byteOffset);
			return {
				opcode: data[0],
				x: view.getFloat32(1, true),
				y: view.getFloat32(5, true),
			};
		},
	},
	18: { name: "CLEAR_NODES", description: "Limpar nós" },
	20: { name: "DRAW_LINE", description: "Desenhar linha" },
	21: { name: "UPDATE_LEADERBOARD", description: "Atualização do leaderboard" },
	// Opcodes de configuração do jogo
	32: {
		name: "SET_BORDER",
		description: "Definir bordas do mapa",
		parser: data => {
			if (data.length < 17) return null;
			const view = new DataView(data.buffer, data.byteOffset);
			return {
				opcode: data[0],
				left: view.getFloat64(1, true),
				top: view.getFloat64(9, true),
			};
		},
	},
	// Opcodes de conexão e sistema
	49: { name: "DISCONNECT", description: "Desconexão" },
	64: { name: "COMPRESSION", description: "Dados comprimidos" },
	240: { name: "HANDSHAKE", description: "Handshake inicial" },
	254: { name: "STATS", description: "Estatísticas do servidor" },
	255: {
		name: "PING",
		description: "Ping/Pong",
		parser: data => {
			return {
				opcode: data[0],
				timestamp: Date.now(),
				size: data.length,
			};
		},
	},
	// Opcodes descobertos através de análise (baseado nos dados fornecidos)
	206: { name: "FREQUENT_UPDATE", description: "Atualização frequente (39 ocorrências)" },
	230: { name: "COMMON_EVENT", description: "Evento comum (21 ocorrências)" },
	134: { name: "REGULAR_UPDATE", description: "Atualização regular (6 ocorrências)" },
	179: { name: "POSITION_SYNC", description: "Sincronização de posição (6 ocorrências)" },
	105: { name: "PLAYER_ACTION", description: "Ação do jogador (4 ocorrências)" },
	171: { name: "GAME_STATE", description: "Estado do jogo (3 ocorrências)" },
	113: { name: "CELL_UPDATE", description: "Atualização de célula (3 ocorrências)" },
	132: { name: "MOVEMENT_DATA", description: "Dados de movimento (3 ocorrências)" },
	235: { name: "SYNC_EVENT", description: "Evento de sincronização (3 ocorrências)" },
};
// ===== INTERCEPTADOR DE WEBSOCKET APRIMORADO =====
class WebSocketInterceptor {
	// Singleton access
	static getInstance() {
		if (!WebSocketInterceptor.instance) {
			WebSocketInterceptor.instance = new WebSocketInterceptor();
		}
		return WebSocketInterceptor.instance;
	}
	constructor() {
		this.interceptedSocket = null;
		this.logInterval = null;
		this.messageBuffer = [];
		this.isLoggingEnabled = true;
		this.detailedLogging = false;
		this.originalWebSocket = window.WebSocket;
		this.interceptWebSocket();
	}
	// Intercepta conexões WebSocket para o servidor alvo
	interceptWebSocket() {
		const self = this;
		window.WebSocket = class extends self.originalWebSocket {
			constructor(url, protocols) {
				super(url, protocols);
				const urlString = url.toString();
				if (urlString.includes("servers.agariobr.com.br:4409")) {
					self.interceptedSocket = this;
					self.startLogging();
					self.attachSocketListeners(this);
				}
			}
		};
		Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
		Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
	}
	// Adiciona listeners para capturar mensagens e eventos do socket
	attachSocketListeners(socket) {
		const originalSend = socket.send.bind(socket);
		socket.send = data => {
			const parsedMessage = DataAnalyzer.parseMessage(data, "sent");
			this.messageBuffer.push(parsedMessage);
			// Log detalhado se habilitado
			if (this.detailedLogging) {
				DataAnalyzer.logRawBuffer(data);
			}
			return originalSend(data);
		};
		socket.addEventListener("message", event => {
			const parsedMessage = DataAnalyzer.parseMessage(event.data, "received");
			this.messageBuffer.push(parsedMessage);
			// Log detalhado se habilitado
			if (this.detailedLogging) {
				DataAnalyzer.logRawBuffer(event.data);
			}
		});
		socket.addEventListener("open", () => {
			console.log("🟢 WebSocket conectado:", socket.url);
		});
		socket.addEventListener("close", event => {
			console.log("🔴 WebSocket desconectado:", { code: event.code, reason: event.reason });
			this.stopLogging();
			this.interceptedSocket = null;
		});
		socket.addEventListener("error", error => {
			console.error("❌ Erro no WebSocket:", error);
		});
	}
	// Inicia logging periódico do status e mensagens
	startLogging() {
		if (this.logInterval) clearInterval(this.logInterval);
		this.logInterval = setInterval(() => {
			if (this.interceptedSocket && this.interceptedSocket.readyState === WebSocket.OPEN) {
				this.logSocketStatus();
				this.logRecentMessages();
			}
		}, 3000);
	}
	stopLogging() {
		if (this.logInterval) {
			clearInterval(this.logInterval);
			this.logInterval = null;
		}
	}
	// Loga status do socket
	logSocketStatus() {
		if (!this.interceptedSocket) return;
		const states = {
			[WebSocket.CONNECTING]: "CONNECTING",
			[WebSocket.OPEN]: "OPEN",
			[WebSocket.CLOSING]: "CLOSING",
			[WebSocket.CLOSED]: "CLOSED",
		};
		console.group("📡 Status WebSocket");
		console.log(`Estado: ${states[this.interceptedSocket.readyState]}`);
		console.log(`URL: ${this.interceptedSocket.url}`);
		console.log(`Protocolo: ${this.interceptedSocket.protocol || "N/A"}`);
		console.log(`Extensões: ${this.interceptedSocket.extensions || "N/A"}`);
		console.log(`Mensagens no buffer: ${this.messageBuffer.length}`);
		console.groupEnd();
	}
	// Loga resumo das mensagens recentes (sem spam)
	logRecentMessages() {
		const now = Date.now();
		const recentMessages = this.messageBuffer.filter(msg => now - msg.timestamp < 3000);
		if (recentMessages.length > 0) {
			// Apenas mostrar estatísticas resumidas
			const opcodeStats = DataAnalyzer.getOpcodeStats(recentMessages);
			const sentCount = recentMessages.filter(msg => msg.type === "sent").length;
			const receivedCount = recentMessages.filter(msg => msg.type === "received").length;
			// Log compacto apenas se houver atividade significativa
			if (recentMessages.length > 5) {
				console.log(
					`📊 Últimos 3s: ${recentMessages.length} msgs (📤${sentCount} 📥${receivedCount}) | Opcodes únicos: ${
						Object.keys(opcodeStats).length
					}`
				);
			}
		}
		// Limitar buffer para evitar uso excessivo de memória
		if (this.messageBuffer.length > 500) {
			this.messageBuffer = this.messageBuffer.slice(-500);
		}
	}
	// Retorna informações do WebSocket interceptado
	getSocketInfo() {
		if (!this.interceptedSocket) return null;
		return {
			url: this.interceptedSocket.url,
			readyState: this.interceptedSocket.readyState,
			protocol: this.interceptedSocket.protocol,
			extensions: this.interceptedSocket.extensions,
			messagesCount: this.messageBuffer.length,
			isLoggingEnabled: this.isLoggingEnabled,
			detailedLogging: this.detailedLogging,
		};
	}
	// Retorna mensagens recentes (em segundos)
	getRecentMessages(seconds = 10) {
		const cutoff = Date.now() - seconds * 1000;
		return this.messageBuffer.filter(msg => msg.timestamp > cutoff);
	}
	// Controles de logging
	enableLogging() {
		this.isLoggingEnabled = true;
		console.log("✅ Logging do WebSocket habilitado");
	}
	disableLogging() {
		this.isLoggingEnabled = false;
		this.stopLogging();
		console.log("❌ Logging do WebSocket desabilitado");
	}
	// Silenciar completamente os logs automáticos
	silenceAutoLogs() {
		this.stopLogging();
		console.log("🔇 Logs automáticos silenciados - use F2/F3 para ver dados manualmente");
	}
	enableDetailedLogging() {
		this.detailedLogging = true;
		console.log("🔍 Logging detalhado habilitado - todas as mensagens serão analisadas");
	}
	disableDetailedLogging() {
		this.detailedLogging = false;
		console.log("🔍 Logging detalhado desabilitado");
	}
	// Análise de estatísticas completas
	getFullStats() {
		const stats = DataAnalyzer.getOpcodeStats(this.messageBuffer);
		const totalMessages = this.messageBuffer.length;
		const sentMessages = this.messageBuffer.filter(msg => msg.type === "sent").length;
		const receivedMessages = this.messageBuffer.filter(msg => msg.type === "received").length;
		return {
			totalMessages,
			sentMessages,
			receivedMessages,
			opcodeStats: stats,
			bufferSize: this.messageBuffer.length,
			oldestMessage: this.messageBuffer.length > 0 ? new Date(this.messageBuffer[0].timestamp) : null,
			newestMessage: this.messageBuffer.length > 0 ? new Date(this.messageBuffer[this.messageBuffer.length - 1].timestamp) : null,
		};
	}
	// Limpar buffer de mensagens
	clearBuffer() {
		this.messageBuffer = [];
		console.log("🗑️ Buffer de mensagens limpo");
	}
}
class UIManager {
	constructor() {
		this.isInitialized = false;
		this.keyBindManager = KeyBindManager.getInstance();
	}
	static getInstance() {
		if (!UIManager.instance) UIManager.instance = new UIManager();
		return UIManager.instance;
	}
	initialize() {
		if (this.isInitialized) return true;
		// F1 - Ajuda com lista de comandos
		this.keyBindManager.register({
			key: "F1",
			handler: () => {
				console.group("🆘 Comandos Disponíveis");
				console.log("F1 - Mostra esta ajuda");
				console.log("F2 - Status do WebSocket");
				console.log("F3 - Estatísticas completas");
				console.log("F4 - Toggle logging detalhado");
				console.log("F5 - Limpar buffer de mensagens");
				console.log("F6 - Analisar opcode específico");
				console.log("F7 - Silenciar logs automáticos");
				console.groupEnd();
			},
			description: "Mostra a ajuda",
		});
		// F2 - Status do WebSocket
		this.keyBindManager.register({
			key: "F2",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				const info = interceptor.getSocketInfo();
				console.log("🔍 Informações do WebSocket:", info);
				const recent = interceptor.getRecentMessages(30);
				console.log("🕒 Mensagens dos últimos 30 segundos:", recent);
			},
			description: "Mostra informações do WebSocket",
		});
		// F3 - Estatísticas completas
		this.keyBindManager.register({
			key: "F3",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				const stats = interceptor.getFullStats();
				console.group("📊 Estatísticas Completas do WebSocket");
				console.log(stats);
				console.groupEnd();
			},
			description: "Mostra estatísticas completas",
		});
		// F4 - Toggle logging detalhado
		this.keyBindManager.register({
			key: "F4",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				const info = interceptor.getSocketInfo();
				if (info === null || info === void 0 ? void 0 : info.detailedLogging) {
					interceptor.disableDetailedLogging();
				} else {
					interceptor.enableDetailedLogging();
				}
			},
			description: "Liga/desliga logging detalhado",
		});
		// F5 - Limpar buffer
		this.keyBindManager.register({
			key: "F5",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				interceptor.clearBuffer();
			},
			description: "Limpa buffer de mensagens",
		});
		// F6 - Analisar opcode específico
		this.keyBindManager.register({
			key: "F6",
			handler: () => {
				const opcode = prompt("Digite o opcode para analisar (número):");
				if (opcode && !isNaN(Number(opcode))) {
					const interceptor = WebSocketInterceptor.getInstance();
					const messages = interceptor.getRecentMessages(60); // últimos 60 segundos
					const filtered = messages.filter(msg => msg.opcode === Number(opcode));
					console.group(`🎯 Análise do Opcode ${opcode}`);
					console.log(`Encontradas ${filtered.length} mensagens com opcode ${opcode}:`);
					filtered.forEach((msg, index) => {
						console.log(`${index + 1}.`, msg);
					});
					console.groupEnd();
				}
			},
			description: "Analisa opcode específico",
		});
		// F7 - Silenciar logs automáticos
		this.keyBindManager.register({
			key: "F7",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				interceptor.silenceAutoLogs();
			},
			description: "Silencia logs automáticos",
		});
		this.isInitialized = true;
		return true;
	}
	destroy() {
		this.isInitialized = false;
	}
}
// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
	constructor() {
		this.isInitialized = false;
		this.settingsStore = SettingsStore.getInstance();
		this.uiManager = UIManager.getInstance();
		this.wsInterceptor = WebSocketInterceptor.getInstance();
	}
	static getInstance() {
		if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
		return ScriptApplication.instance;
	}
	async initialize() {
		if (this.isInitialized) return true;
		if (document.readyState === "loading") {
			await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
		}
		this.uiManager.initialize();
		this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
		console.group("🚀 Script Avançado de Análise WebSocket Inicializado");
		console.log(`📦 Versão: ${CONFIG.VERSION}`);
		console.log("🔧 Funcionalidades:");
		console.log("  • Interceptação automática de WebSocket");
		console.log("  • Análise de opcodes em tempo real");
		console.log("  • Parsing inteligente de dados");
		console.log("  • Logging detalhado configurável");
		console.log("  • Estatísticas de tráfego");
		console.log("");
		console.log("⌨️  Comandos disponíveis:");
		console.log("  F1 - Ajuda completa");
		console.log("  F2 - Status do WebSocket");
		console.log("  F3 - Estatísticas completas");
		console.log("  F4 - Toggle logging detalhado");
		console.log("  F5 - Limpar buffer");
		console.log("  F6 - Analisar opcode específico");
		console.log("  F7 - Silenciar logs automáticos");
		console.log("");
		console.log("💡 Dica: Use F7 para silenciar logs automáticos e evitar spam no console!");
		console.groupEnd();
		this.isInitialized = true;
		return true;
	}
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
